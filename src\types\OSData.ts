// 操作系统节点数据类型定义

export interface OSNode {
  id: string;
  name: string;
  fullName: string;
  year: number;
  description: string;
  keyFeatures: string[];
  keyPeople: string[];
  company: string;
  category: OSCategory;
  influences: string[]; // 影响来源的OS ID列表
  influenced: string[]; // 被影响的OS ID列表
  position: {
    x: number;
    y: number;
    z: number;
  };
  color: string;
  significance: number; // 1-10，历史重要性评分
  isCommercial: boolean;
  isOpenSource: boolean;
  status: OSStatus;
}

export enum OSCategory {
  MAINFRAME = 'mainframe',
  MINICOMPUTER = 'minicomputer', 
  PERSONAL = 'personal',
  MOBILE = 'mobile',
  EMBEDDED = 'embedded',
  REALTIME = 'realtime',
  DISTRIBUTED = 'distributed',
  RESEARCH = 'research'
}

export enum OSStatus {
  ACTIVE = 'active',
  DISCONTINUED = 'discontinued',
  LEGACY = 'legacy',
  RESEARCH = 'research'
}

export interface OSConnection {
  from: string;
  to: string;
  type: ConnectionType;
  strength: number; // 1-10，影响强度
  description: string;
}

export enum ConnectionType {
  DIRECT_EVOLUTION = 'direct_evolution',    // 直接演化
  INSPIRATION = 'inspiration',              // 灵感来源
  FORK = 'fork',                           // 分支
  MERGER = 'merger',                       // 合并
  REIMPLEMENTATION = 'reimplementation',    // 重新实现
  CONCEPT_ADOPTION = 'concept_adoption'     // 概念采用
}

export interface TimelineEvent {
  year: number;
  osId: string;
  eventType: 'release' | 'update' | 'discontinue';
  description: string;
}

export interface OSDataSet {
  nodes: OSNode[];
  connections: OSConnection[];
  timeline: TimelineEvent[];
  yearRange: {
    min: number;
    max: number;
  };
}
