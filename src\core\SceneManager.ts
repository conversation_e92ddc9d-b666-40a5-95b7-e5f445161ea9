import * as THREE from 'three';

export class SceneManager {
  public scene: THREE.Scene;
  public camera: THREE.PerspectiveCamera;
  public renderer: THREE.WebGLRenderer;
  public controls: any; // OrbitControls类型稍后添加
  
  private container: HTMLElement;
  private animationId: number | null = null;
  private onRenderCallbacks: (() => void)[] = [];

  constructor(container: HTMLElement) {
    this.container = container;
    this.scene = new THREE.Scene();
    this.camera = new THREE.PerspectiveCamera();
    this.renderer = new THREE.WebGLRenderer();
    
    this.initScene();
    this.initCamera();
    this.initRenderer();
    this.initLights();
    this.initControls();
    this.setupEventListeners();
    
    this.startRenderLoop();
  }

  private initScene(): void {
    // 设置场景背景为深空效果
    this.scene.background = new THREE.Color(0x0a0a0a);
    
    // 添加雾效果增强深度感
    this.scene.fog = new THREE.Fog(0x0a0a0a, 50, 200);
  }

  private initCamera(): void {
    const aspect = this.container.clientWidth / this.container.clientHeight;
    this.camera = new THREE.PerspectiveCamera(75, aspect, 0.1, 1000);
    
    // 设置相机初始位置
    this.camera.position.set(0, 20, 50);
    this.camera.lookAt(0, 0, 0);
  }

  private initRenderer(): void {
    this.renderer = new THREE.WebGLRenderer({ 
      antialias: true,
      alpha: true 
    });
    
    this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    
    // 启用阴影
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    
    // 设置色调映射
    this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
    this.renderer.toneMappingExposure = 1.2;
    
    this.container.appendChild(this.renderer.domElement);
  }

  private initLights(): void {
    // 环境光 - 提供基础照明
    const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
    this.scene.add(ambientLight);

    // 主方向光 - 模拟太阳光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(50, 50, 50);
    directionalLight.castShadow = true;
    
    // 配置阴影
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 500;
    directionalLight.shadow.camera.left = -100;
    directionalLight.shadow.camera.right = 100;
    directionalLight.shadow.camera.top = 100;
    directionalLight.shadow.camera.bottom = -100;
    
    this.scene.add(directionalLight);

    // 补充光源 - 从另一个角度照亮场景
    const fillLight = new THREE.DirectionalLight(0x64ffda, 0.3);
    fillLight.position.set(-30, 20, -30);
    this.scene.add(fillLight);

    // 点光源 - 为特定区域提供重点照明
    const pointLight = new THREE.PointLight(0x64ffda, 0.5, 100);
    pointLight.position.set(0, 30, 0);
    this.scene.add(pointLight);
  }

  private async initControls(): Promise<void> {
    // 动态导入OrbitControls
    const { OrbitControls } = await import('three/examples/jsm/controls/OrbitControls.js');
    
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    
    // 配置控制器
    this.controls.enableDamping = true;
    this.controls.dampingFactor = 0.05;
    this.controls.screenSpacePanning = false;
    
    // 限制相机移动范围
    this.controls.minDistance = 10;
    this.controls.maxDistance = 200;
    this.controls.maxPolarAngle = Math.PI;
    
    // 设置自动旋转
    this.controls.autoRotate = false;
    this.controls.autoRotateSpeed = 0.5;
  }

  private setupEventListeners(): void {
    // 窗口大小调整
    window.addEventListener('resize', this.onWindowResize.bind(this));
    
    // 鼠标事件
    this.renderer.domElement.addEventListener('click', this.onMouseClick.bind(this));
    this.renderer.domElement.addEventListener('mousemove', this.onMouseMove.bind(this));
  }

  private onWindowResize(): void {
    const width = this.container.clientWidth;
    const height = this.container.clientHeight;
    
    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();
    
    this.renderer.setSize(width, height);
  }

  private onMouseClick(event: MouseEvent): void {
    // 鼠标点击事件处理，稍后实现射线检测
    const rect = this.renderer.domElement.getBoundingClientRect();
    const mouse = new THREE.Vector2(
      ((event.clientX - rect.left) / rect.width) * 2 - 1,
      -((event.clientY - rect.top) / rect.height) * 2 + 1
    );
    
    // 触发自定义点击事件
    this.container.dispatchEvent(new CustomEvent('sceneClick', { 
      detail: { mouse, originalEvent: event } 
    }));
  }

  private onMouseMove(event: MouseEvent): void {
    // 鼠标移动事件处理
    const rect = this.renderer.domElement.getBoundingClientRect();
    const mouse = new THREE.Vector2(
      ((event.clientX - rect.left) / rect.width) * 2 - 1,
      -((event.clientY - rect.top) / rect.height) * 2 + 1
    );
    
    // 触发自定义移动事件
    this.container.dispatchEvent(new CustomEvent('sceneMouseMove', { 
      detail: { mouse, originalEvent: event } 
    }));
  }

  public addRenderCallback(callback: () => void): void {
    this.onRenderCallbacks.push(callback);
  }

  public removeRenderCallback(callback: () => void): void {
    const index = this.onRenderCallbacks.indexOf(callback);
    if (index > -1) {
      this.onRenderCallbacks.splice(index, 1);
    }
  }

  private startRenderLoop(): void {
    const animate = () => {
      this.animationId = requestAnimationFrame(animate);
      
      // 更新控制器
      if (this.controls) {
        this.controls.update();
      }
      
      // 执行渲染回调
      this.onRenderCallbacks.forEach(callback => callback());
      
      // 渲染场景
      this.renderer.render(this.scene, this.camera);
    };
    
    animate();
  }

  public dispose(): void {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }
    
    window.removeEventListener('resize', this.onWindowResize.bind(this));
    
    if (this.controls) {
      this.controls.dispose();
    }
    
    this.renderer.dispose();
    
    if (this.container.contains(this.renderer.domElement)) {
      this.container.removeChild(this.renderer.domElement);
    }
  }

  // 相机动画方法
  public animateCameraTo(position: THREE.Vector3, target: THREE.Vector3, duration: number = 1000): Promise<void> {
    return new Promise((resolve) => {
      const startPosition = this.camera.position.clone();
      const startTarget = this.controls.target.clone();
      const startTime = Date.now();
      
      const animate = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // 使用缓动函数
        const easeProgress = 1 - Math.pow(1 - progress, 3);
        
        this.camera.position.lerpVectors(startPosition, position, easeProgress);
        this.controls.target.lerpVectors(startTarget, target, easeProgress);
        this.controls.update();
        
        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          resolve();
        }
      };
      
      animate();
    });
  }

  // 重置相机视角
  public resetCamera(): void {
    this.animateCameraTo(
      new THREE.Vector3(0, 20, 50),
      new THREE.Vector3(0, 0, 0),
      1500
    );
  }

  // 启用/禁用自动旋转
  public setAutoRotate(enabled: boolean): void {
    if (this.controls) {
      this.controls.autoRotate = enabled;
    }
  }
}
