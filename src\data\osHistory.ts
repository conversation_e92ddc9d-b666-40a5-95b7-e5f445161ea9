import { OSNode, OSConnection, OSDataSet, OSCategory, OSStatus, ConnectionType } from '../types/OSData';

// 操作系统历史数据
export const osNodes: OSNode[] = [
  {
    id: 'gm-os',
    name: 'GM-NAA I/O',
    fullName: 'General Motors-North American Aviation I/O System',
    year: 1956,
    description: '第一个真正的操作系统，为IBM 704大型机开发',
    keyFeatures: ['批处理', '自动作业调度', '输入输出控制'],
    keyPeople: ['<PERSON>', '<PERSON>'],
    company: 'General Motors & North American Aviation',
    category: OSCategory.MAINFRAME,
    influences: [],
    influenced: ['ibm-7090', 'ctss'],
    position: { x: -50, y: 0, z: 0 },
    color: '#8B4513',
    significance: 8,
    isCommercial: true,
    isOpenSource: false,
    status: OSStatus.DISCONTINUED
  },
  {
    id: 'ctss',
    name: 'CT<PERSON>',
    fullName: 'Compatible Time-Sharing System',
    year: 1961,
    description: 'MIT开发的分时系统，现代操作系统的重要里程碑',
    keyFeatures: ['分时处理', '多用户', '虚拟内存', '文件系统'],
    keyPeople: ['Fernando Corbató', 'Marjorie Merwin-Dawe'],
    company: 'MIT',
    category: OSCategory.MAINFRAME,
    influences: ['gm-os'],
    influenced: ['multics', 'unix'],
    position: { x: -30, y: 10, z: 0 },
    color: '#4169E1',
    significance: 9,
    isCommercial: false,
    isOpenSource: false,
    status: OSStatus.DISCONTINUED
  },
  {
    id: 'multics',
    name: 'Multics',
    fullName: 'Multiplexed Information and Computing Service',
    year: 1965,
    description: '雄心勃勃的分时系统项目，UNIX的重要前身',
    keyFeatures: ['分层文件系统', '动态链接', '安全机制', '虚拟内存'],
    keyPeople: ['Fernando Corbató', 'Robert Daley', 'Peter Neumann'],
    company: 'MIT, Bell Labs, GE',
    category: OSCategory.MAINFRAME,
    influences: ['ctss'],
    influenced: ['unix'],
    position: { x: -10, y: 20, z: 0 },
    color: '#9932CC',
    significance: 8,
    isCommercial: true,
    isOpenSource: false,
    status: OSStatus.DISCONTINUED
  },
  {
    id: 'unix',
    name: 'UNIX',
    fullName: 'Uniplexed Information Computing System',
    year: 1969,
    description: '最具影响力的操作系统之一，现代OS的基石',
    keyFeatures: ['管道', '文件系统', 'Shell', '多用户', '可移植性'],
    keyPeople: ['Ken Thompson', 'Dennis Ritchie', 'Brian Kernighan'],
    company: 'Bell Labs',
    category: OSCategory.MINICOMPUTER,
    influences: ['multics', 'ctss'],
    influenced: ['bsd', 'linux', 'macos', 'solaris', 'aix'],
    position: { x: 10, y: 30, z: 0 },
    color: '#FF6347',
    significance: 10,
    isCommercial: true,
    isOpenSource: false,
    status: OSStatus.LEGACY
  },
  {
    id: 'cp-m',
    name: 'CP/M',
    fullName: 'Control Program for Microcomputers',
    year: 1974,
    description: '早期微型计算机的标准操作系统',
    keyFeatures: ['文件管理', '命令行界面', '程序加载'],
    keyPeople: ['Gary Kildall'],
    company: 'Digital Research',
    category: OSCategory.PERSONAL,
    influences: [],
    influenced: ['ms-dos'],
    position: { x: -20, y: -10, z: 0 },
    color: '#DAA520',
    significance: 7,
    isCommercial: true,
    isOpenSource: false,
    status: OSStatus.DISCONTINUED
  },
  {
    id: 'bsd',
    name: 'BSD',
    fullName: 'Berkeley Software Distribution',
    year: 1977,
    description: 'UNIX的重要分支，网络功能的先驱',
    keyFeatures: ['TCP/IP协议栈', '虚拟内存', 'C Shell', '套接字'],
    keyPeople: ['Bill Joy', 'Eric Allman', 'Marshall Kirk McKusick'],
    company: 'UC Berkeley',
    category: OSCategory.MINICOMPUTER,
    influences: ['unix'],
    influenced: ['freebsd', 'openbsd', 'netbsd', 'macos'],
    position: { x: 30, y: 40, z: 10 },
    color: '#FF4500',
    significance: 9,
    isCommercial: false,
    isOpenSource: true,
    status: OSStatus.LEGACY
  },
  {
    id: 'ms-dos',
    name: 'MS-DOS',
    fullName: 'Microsoft Disk Operating System',
    year: 1981,
    description: 'IBM PC的标准操作系统，微软的起点',
    keyFeatures: ['文件分配表', '批处理', '命令行界面'],
    keyPeople: ['Tim Paterson', 'Bill Gates'],
    company: 'Microsoft',
    category: OSCategory.PERSONAL,
    influences: ['cp-m'],
    influenced: ['windows'],
    position: { x: -10, y: -20, z: 0 },
    color: '#000080',
    significance: 8,
    isCommercial: true,
    isOpenSource: false,
    status: OSStatus.DISCONTINUED
  },
  {
    id: 'lisa-os',
    name: 'Lisa OS',
    fullName: 'Lisa Office System',
    year: 1983,
    description: '苹果的第一个图形用户界面操作系统',
    keyFeatures: ['图形界面', '鼠标操作', '多任务', '虚拟内存'],
    keyPeople: ['Steve Jobs', 'Larry Tesler'],
    company: 'Apple',
    category: OSCategory.PERSONAL,
    influences: ['xerox-star'],
    influenced: ['macos'],
    position: { x: -40, y: -30, z: 0 },
    color: '#FF69B4',
    significance: 6,
    isCommercial: true,
    isOpenSource: false,
    status: OSStatus.DISCONTINUED
  },
  {
    id: 'windows',
    name: 'Windows',
    fullName: 'Microsoft Windows',
    year: 1985,
    description: '世界上最广泛使用的桌面操作系统',
    keyFeatures: ['图形界面', '多任务', '即插即用', '注册表'],
    keyPeople: ['Bill Gates', 'Scott McGregor'],
    company: 'Microsoft',
    category: OSCategory.PERSONAL,
    influences: ['ms-dos', 'xerox-star'],
    influenced: ['windows-nt', 'windows-ce'],
    position: { x: 0, y: -30, z: 0 },
    color: '#0078D4',
    significance: 10,
    isCommercial: true,
    isOpenSource: false,
    status: OSStatus.ACTIVE
  },
  {
    id: 'linux',
    name: 'Linux',
    fullName: 'Linux Kernel',
    year: 1991,
    description: '开源UNIX-like操作系统，服务器和移动设备的主力',
    keyFeatures: ['开源', '可移植性', '多用户', '网络支持', '模块化'],
    keyPeople: ['Linus Torvalds', 'Alan Cox', 'Andrew Morton'],
    company: 'Linux Foundation',
    category: OSCategory.PERSONAL,
    influences: ['unix', 'minix'],
    influenced: ['android', 'ubuntu', 'redhat'],
    position: { x: 20, y: 50, z: 0 },
    color: '#FCC624',
    significance: 10,
    isCommercial: false,
    isOpenSource: true,
    status: OSStatus.ACTIVE
  },
  {
    id: 'macos',
    name: 'macOS',
    fullName: 'macOS (formerly Mac OS X)',
    year: 2001,
    description: '苹果的现代操作系统，基于BSD和NeXTSTEP',
    keyFeatures: ['Aqua界面', 'Darwin内核', 'Cocoa框架', 'Spotlight搜索'],
    keyPeople: ['Steve Jobs', 'Avie Tevanian', 'Craig Federighi'],
    company: 'Apple',
    category: OSCategory.PERSONAL,
    influences: ['unix', 'bsd', 'nextstep', 'lisa-os'],
    influenced: ['ios'],
    position: { x: 40, y: 20, z: 0 },
    color: '#007AFF',
    significance: 9,
    isCommercial: true,
    isOpenSource: false,
    status: OSStatus.ACTIVE
  },
  {
    id: 'android',
    name: 'Android',
    fullName: 'Android Operating System',
    year: 2008,
    description: '基于Linux的移动操作系统，全球最大的移动平台',
    keyFeatures: ['触摸界面', 'Java应用', '应用商店', '多任务'],
    keyPeople: ['Andy Rubin', 'Rich Miner', 'Nick Sears'],
    company: 'Google',
    category: OSCategory.MOBILE,
    influences: ['linux'],
    influenced: [],
    position: { x: 30, y: 60, z: 0 },
    color: '#3DDC84',
    significance: 9,
    isCommercial: true,
    isOpenSource: true,
    status: OSStatus.ACTIVE
  },
  {
    id: 'ios',
    name: 'iOS',
    fullName: 'iPhone Operating System',
    year: 2007,
    description: '苹果的移动操作系统，重新定义了智能手机',
    keyFeatures: ['多点触控', 'App Store', '沙盒机制', '推送通知'],
    keyPeople: ['Steve Jobs', 'Scott Forstall', 'Tony Fadell'],
    company: 'Apple',
    category: OSCategory.MOBILE,
    influences: ['macos'],
    influenced: [],
    position: { x: 50, y: 30, z: 0 },
    color: '#FF3B30',
    significance: 9,
    isCommercial: true,
    isOpenSource: false,
    status: OSStatus.ACTIVE
  }
];

export const osConnections: OSConnection[] = [
  { from: 'gm-os', to: 'ctss', type: ConnectionType.INSPIRATION, strength: 7, description: '批处理系统的经验启发了分时系统的设计' },
  { from: 'ctss', to: 'multics', type: ConnectionType.DIRECT_EVOLUTION, strength: 9, description: 'CTSS的直接后继者，扩展了分时概念' },
  { from: 'multics', to: 'unix', type: ConnectionType.INSPIRATION, strength: 8, description: 'UNIX从Multics的复杂性中学习，追求简洁' },
  { from: 'ctss', to: 'unix', type: ConnectionType.CONCEPT_ADOPTION, strength: 6, description: '采用了分时和多用户概念' },
  { from: 'unix', to: 'bsd', type: ConnectionType.FORK, strength: 9, description: 'BSD是UNIX的重要分支' },
  { from: 'unix', to: 'linux', type: ConnectionType.REIMPLEMENTATION, strength: 8, description: 'Linux重新实现了UNIX的功能' },
  { from: 'bsd', to: 'macos', type: ConnectionType.CONCEPT_ADOPTION, strength: 7, description: 'macOS采用了BSD的网络栈和部分内核' },
  { from: 'cp-m', to: 'ms-dos', type: ConnectionType.INSPIRATION, strength: 8, description: 'MS-DOS借鉴了CP/M的设计理念' },
  { from: 'ms-dos', to: 'windows', type: ConnectionType.DIRECT_EVOLUTION, strength: 9, description: 'Windows最初运行在MS-DOS之上' },
  { from: 'lisa-os', to: 'macos', type: ConnectionType.CONCEPT_ADOPTION, strength: 6, description: 'macOS继承了Lisa OS的GUI概念' },
  { from: 'linux', to: 'android', type: ConnectionType.DIRECT_EVOLUTION, strength: 9, description: 'Android基于Linux内核' },
  { from: 'macos', to: 'ios', type: ConnectionType.DIRECT_EVOLUTION, strength: 8, description: 'iOS基于macOS的Darwin内核' }
];

export const osDataSet: OSDataSet = {
  nodes: osNodes,
  connections: osConnections,
  timeline: osNodes.map(node => ({
    year: node.year,
    osId: node.id,
    eventType: 'release' as const,
    description: `${node.name} 发布`
  })),
  yearRange: {
    min: 1956,
    max: 2024
  }
};
