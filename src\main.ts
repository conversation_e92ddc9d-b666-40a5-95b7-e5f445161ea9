import './style.css';
import { OSTimeShuttle } from './OSTimeShuttle';

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', () => {
  const app = document.querySelector<HTMLDivElement>('#app')!;

  // 创建OS时光穿梭机应用
  const timeShuttle = new OSTimeShuttle(app);

  // 初始化应用
  timeShuttle.init().then(() => {
    console.log('OS时光穿梭机初始化完成');
  }).catch((error) => {
    console.error('初始化失败:', error);
  });
});
